import { auth } from "@/lib/auth";
import { getUserAccountData } from "@/utils/db/account-queries";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import MyAccountClientPage from "./ClientPage";

export default async function MyAccountPage() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    redirect("/auth/signin");
  }

  let accountData = null;
  try {
    accountData = await getUserAccountData(session.user.id);
  } catch (error) {
    console.error("Error fetching account data:", error);
  }

  return (
    <MyAccountClientPage initialAccountData={accountData} user={session.user} />
  );
}
