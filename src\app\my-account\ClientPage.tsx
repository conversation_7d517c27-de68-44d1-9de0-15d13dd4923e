"use client";

import {
  AccountSidebar,
  AccountSection,
} from "@/components/account/account-sidebar";
import { AccountDetailsForm } from "@/components/account/account-details-form";
import { PasswordChangeForm } from "@/components/account/password-change-form";
import { ProfilePictureUpload } from "@/components/account/profile-picture-upload";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { authUtils } from "@/lib/auth-utils";
import {
  AccountDetailsFormData,
  PasswordChangeFormData,
  getAccountCapabilities,
  getImageSourceType,
  UserAccountData,
} from "@/lib/account-schemas";
import { useProfilePicture } from "@/hooks/use-profile-picture";
import { useState } from "react";
import { useRouter } from "next/navigation";

interface MyAccountClientPageProps {
  initialAccountData: UserAccountData | null;
  user: {
    id: string;
    name: string;
    email: string;
    username?: string | null;
    image?: string | null;
    createdAt: Date;
    updatedAt: Date;
    emailVerified: boolean;
    displayUsername?: string | null;
  };
}

export default function MyAccountClientPage({
  initialAccountData,
  user,
}: MyAccountClientPageProps) {
  const [activeSection, setActiveSection] =
    useState<AccountSection>("detalii-cont");
  const [accountData, setAccountData] = useState<UserAccountData | null>(
    initialAccountData
  );
  const [isUpdating, setIsUpdating] = useState(false);
  const [uploadError, setUploadError] = useState<string>();
  const { refreshProfilePicture } = useProfilePicture();

  const router = useRouter();

  const refreshAccountData = async () => {
    try {
      const response = await fetch("/api/account/data");
      const result = await response.json();

      if (response.ok && result.success) {
        setAccountData(result.data);
      }
    } catch (error) {
      console.error("Error refreshing account data:", error);
    }
  };

  const handleSignOut = async () => {
    try {
      await authUtils.signOut();
      router.push("/auth/signin");
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  const handleProfileUpdate = async (data: AccountDetailsFormData) => {
    setIsUpdating(true);
    try {
      const response = await fetch("/api/account/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Eroare la actualizarea profilului");
      }

      await refreshAccountData();
    } catch (error) {
      console.error("Error updating profile:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePasswordChange = async (data: PasswordChangeFormData) => {
    setIsUpdating(true);
    try {
      const response = await fetch("/api/account/password", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Eroare la schimbarea parolei");
      }

      alert("Parola a fost schimbată cu succes!");
    } catch (error) {
      console.error("Error changing password:", error);
      alert(error instanceof Error ? error.message : "A apărut o eroare");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleImageUpload = async (file: File) => {
    setIsUpdating(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/account/profile-picture", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        console.log(
          result.error + result.details.length > 0
            ? " " + result.details[0].message
            : ""
        );
        setUploadError(
          `${result.error}. ${
            result.details.length > 0 ? result.details[0].message : ""
          }`
        );
        throw new Error(result.error || "Eroare la încărcarea imaginii");
      }

      setUploadError(undefined);
      await refreshAccountData();
    } catch (error) {
      console.error("Error uploading image:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleImageRemove = async () => {
    if (!accountData?.profile_picture?.user_id) return;

    setIsUpdating(true);
    try {
      const response = await fetch("/api/account/profile-picture", {
        method: "DELETE",
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Eroare la ștergerea imaginii");
      }

      await refreshAccountData();
    } catch (error) {
      console.error("Error removing image:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  if (isUpdating && !accountData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-portavio-orange mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Se încarcă...</p>
        </div>
      </div>
    );
  }

  const capabilities = accountData
    ? getAccountCapabilities(accountData.accounts || [])
    : {
        canChangeEmail: false,
        canChangePassword: false,
        canChangeUsername: true,
        canChangeName: true,
        canUploadProfilePicture: true,
      };

  const currentImage =
    accountData?.profile_picture?.image_data ||
    accountData?.image ||
    user?.image ||
    undefined;

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-5xl font-bold text-foreground">Contul meu</h1>
            </div>
            <Button onClick={handleSignOut} variant="outline">
              Deconectează-te
            </Button>
          </div>

          <div className="flex flex-col lg:flex-row gap-8">
            <AccountSidebar
              activeSection={activeSection}
              onSectionChange={setActiveSection}
            />

            <div className="flex-1">
              <Card className="shadow-lg">
                <CardHeader>
                  <CardTitle className="text-xl">
                    {activeSection === "detalii-cont" && "Detalii cont"}
                    {activeSection === "schimbare-parola" && "Schimbare parolă"}
                  </CardTitle>
                  <CardDescription>
                    {activeSection === "detalii-cont" &&
                      "Gestionează informațiile contului tău"}
                    {activeSection === "schimbare-parola" &&
                      "Actualizează parola contului tău"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {activeSection === "detalii-cont" && accountData && (
                    <div className="lg:flex gap-16 justify-between w-full">
                      <AccountDetailsForm
                        initialData={{
                          email: accountData.email,
                          username: accountData.username || "",
                          name: accountData.name,
                          createdAt: accountData.createdAt,
                        }}
                        capabilities={capabilities}
                        onSubmit={handleProfileUpdate}
                        isLoading={isUpdating}
                      />

                      <div className="flex flex-col items-center">
                        <ProfilePictureUpload
                          currentImage={currentImage}
                          username={accountData.username || undefined}
                          name={accountData.name}
                          onImageUpload={handleImageUpload}
                          onImageRemove={
                            getImageSourceType(currentImage) === "base64"
                              ? handleImageRemove
                              : undefined
                          }
                          onImageChange={refreshProfilePicture}
                          isLoading={isUpdating}
                        />
                        {uploadError && (
                          <p className="text-sm text-red-600 dark:text-red-400">
                            {uploadError}
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {activeSection === "schimbare-parola" && (
                    <PasswordChangeForm
                      onSubmit={handlePasswordChange}
                      isLoading={isUpdating}
                      canChangePassword={capabilities.canChangePassword}
                    />
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
